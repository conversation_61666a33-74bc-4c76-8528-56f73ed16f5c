<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro - Settings</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <h1>
                    <div class="shield-icon">🛡️</div>
                    FocusGuard Pro
                </h1>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#overview" class="nav-link active" data-section="overview">
                        <span class="nav-icon">📊</span>
                        Overview
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#blocking" class="nav-link" data-section="blocking">
                        <span class="nav-icon">🚫</span>
                        Block List
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#general" class="nav-link" data-section="general">
                        <span class="nav-icon">⚙️</span>
                        General
                    </a>
                </li>
            </ul>
        </nav>

        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="page-header">
                    <h1 class="page-title">Overview</h1>
                    <p class="page-subtitle">Your productivity statistics at a glance</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Quick Stats</h2>
                        <p class="section-description">Your FocusGuard Pro activity summary</p>
                    </div>
                    <div class="section-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="blockedSitesCount">0</div>
                                <div class="stat-label">Sites Blocked</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="blocksToday">0</div>
                                <div class="stat-label">Blocks Today</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="timeSaved">0h</div>
                                <div class="stat-label">Time Saved</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="successRate">100%</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Extension Status</h2>
                        <p class="section-description">Current protection status</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>FocusGuard Protection</h4>
                                <p>Enable or disable website blocking</p>
                            </div>
                            <div class="toggle-switch active" id="mainProtectionToggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Block List Section -->
            <section id="blocking" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Block List</h1>
                    <p class="page-subtitle">Manage websites you want to block</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Add Website</h2>
                        <p class="section-description">Block new websites to improve your focus</p>
                    </div>
                    <div class="section-content">
                        <div class="input-group">
                            <label class="input-label">Website URL</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="newSiteInput" class="input-field" placeholder="Enter website URL (e.g., facebook.com)">
                                <button id="addSiteBtn" class="btn btn-primary">Add Site</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Blocked Websites</h2>
                        <p class="section-description">Currently blocked websites</p>
                    </div>
                    <div class="section-content">
                        <div class="site-list" id="blockedSitesList">
                            <div class="empty-state" id="emptyBlockedSites">
                                <div class="empty-icon">🎯</div>
                                <p>No sites blocked yet</p>
                                <small>Add websites above to start focusing</small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- General Settings Section -->
            <section id="general" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">General Settings</h1>
                    <p class="page-subtitle">Configure general extension behavior</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Blocking Behavior</h2>
                        <p class="section-description">How FocusGuard should handle blocked sites</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Work in Incognito Mode</h4>
                                <p>Apply blocking rules in private browsing mode</p>
                            </div>
                            <div class="toggle-switch active" id="incognitoToggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Show Notifications</h4>
                                <p>Display notifications for blocked sites and alerts</p>
                            </div>
                            <div class="toggle-switch active" id="notificationsToggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="input-group">
                            <label class="input-label">Default Block Duration</label>
                            <select id="defaultBlockDuration" class="input-field">
                                <option value="1hour">1 hour</option>
                                <option value="24hours">24 hours</option>
                                <option value="5days" selected>5 days</option>
                                <option value="1week">1 week</option>
                                <option value="permanent">Permanent</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Data & Privacy</h2>
                        <p class="section-description">Manage your data and privacy settings</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Analytics</h4>
                                <p>Track time spent on websites and blocking statistics</p>
                            </div>
                            <div class="toggle-switch active" id="analyticsToggle">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button id="exportDataBtn" class="btn btn-secondary">Export Data</button>
                            <button id="importDataBtn" class="btn btn-secondary">Import Data</button>
                        </div>
                    </div>
                </div>

                <!-- Danger Zone -->
                <div class="settings-section danger-zone">
                    <div class="section-header">
                        <h2 class="section-title">Danger Zone</h2>
                        <p class="section-description">Irreversible and destructive actions</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Reset All Settings</h4>
                                <p>This will reset all your settings to default values</p>
                            </div>
                            <button id="resetSettingsBtn" class="btn btn-secondary">Reset Settings</button>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Clear All Data</h4>
                                <p>Permanently delete all blocking data and statistics</p>
                            </div>
                            <button id="clearDataBtn" class="btn btn-danger">Clear Data</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Hidden file input for import -->
    <input type="file" id="importFileInput" accept=".json" style="display: none;">

    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>Loading...</p>
    </div>

    <script src="../utils/storage.js"></script>
    <script src="options.js"></script>
</body>
</html>
