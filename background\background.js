// FocusGuard Pro - Background Service Worker
// Phase 1: Basic site blocking functionality

class FocusGuardBackground {
  constructor() {
    this.blockedSites = new Set();
    this.isEnabled = true;
    this.init();
  }

  async init() {
    // Load saved settings and blocked sites
    await this.loadSettings();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Initialize blocking rules
    await this.updateBlockingRules();
    
    console.log('FocusGuard Pro initialized');
  }

  async loadSettings() {
    try {
      const result = await chrome.storage.local.get([
        'blockedSites',
        'isEnabled',
        'settings'
      ]);
      
      this.blockedSites = new Set(result.blockedSites || []);
      this.isEnabled = result.isEnabled !== false; // Default to true
      this.settings = result.settings || this.getDefaultSettings();
      
      console.log('Settings loaded:', {
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled
      });
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  getDefaultSettings() {
    return {
      defaultBlockDuration: '5days',
      showNotifications: true,
      workInIncognito: true,
      enableTimeTracking: true
    };
  }

  setupEventListeners() {
    // Listen for tab updates to check for blocked sites
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'loading' && tab.url) {
        this.checkAndBlockSite(tabId, tab.url);
      }
    });

    // Listen for navigation events
    chrome.webNavigation.onBeforeNavigate.addListener((details) => {
      if (details.frameId === 0) { // Main frame only
        this.checkAndBlockSite(details.tabId, details.url);
      }
    });

    // Listen for messages from popup and content scripts
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Keep message channel open for async response
    });

    // Handle extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'addBlockedSite':
          await this.addBlockedSite(request.url);
          sendResponse({ success: true });
          break;
          
        case 'removeBlockedSite':
          await this.removeBlockedSite(request.url);
          sendResponse({ success: true });
          break;
          
        case 'getBlockedSites':
          sendResponse({ 
            sites: Array.from(this.blockedSites),
            isEnabled: this.isEnabled 
          });
          break;
          
        case 'toggleBlocking':
          await this.toggleBlocking();
          sendResponse({ success: true, isEnabled: this.isEnabled });
          break;
          
        case 'checkSiteStatus':
          const isBlocked = this.isSiteBlocked(request.url);
          sendResponse({ isBlocked });
          break;
          
        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ error: error.message });
    }
  }

  async addBlockedSite(url) {
    const domain = this.extractDomain(url);
    if (domain) {
      this.blockedSites.add(domain);
      await this.saveSettings();
      await this.updateBlockingRules();
      
      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been blocked`
        });
      }
    }
  }

  async removeBlockedSite(url) {
    const domain = this.extractDomain(url);
    if (domain && this.blockedSites.has(domain)) {
      this.blockedSites.delete(domain);
      await this.saveSettings();
      await this.updateBlockingRules();
      
      // Show notification
      if (this.settings.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'FocusGuard Pro',
          message: `${domain} has been unblocked`
        });
      }
    }
  }

  async toggleBlocking() {
    this.isEnabled = !this.isEnabled;
    await this.saveSettings();
    await this.updateBlockingRules();
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      console.error('Invalid URL:', url);
      return null;
    }
  }

  isSiteBlocked(url) {
    if (!this.isEnabled) return false;
    
    const domain = this.extractDomain(url);
    if (!domain) return false;
    
    // Check exact match
    if (this.blockedSites.has(domain)) return true;
    
    // Check subdomain match
    for (const blockedDomain of this.blockedSites) {
      if (domain.endsWith('.' + blockedDomain) || domain === blockedDomain) {
        return true;
      }
    }
    
    return false;
  }

  async checkAndBlockSite(tabId, url) {
    if (this.isSiteBlocked(url)) {
      // Redirect to blocked page
      const blockedPageUrl = chrome.runtime.getURL('blocked/blocked.html') + 
                           '?blocked=' + encodeURIComponent(url);
      
      try {
        await chrome.tabs.update(tabId, { url: blockedPageUrl });
      } catch (error) {
        console.error('Error redirecting to blocked page:', error);
      }
    }
  }

  async updateBlockingRules() {
    // This will be used for declarativeNetRequest rules in future phases
    // For now, we handle blocking through tab redirection
    console.log('Blocking rules updated for', this.blockedSites.size, 'sites');
  }

  async saveSettings() {
    try {
      await chrome.storage.local.set({
        blockedSites: Array.from(this.blockedSites),
        isEnabled: this.isEnabled,
        settings: this.settings
      });
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  async handleFirstInstall() {
    // Set up default settings and show welcome notification
    await this.saveSettings();
    
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Welcome to FocusGuard Pro!',
      message: 'Click the extension icon to start blocking distracting websites.'
    });
    
    // Open options page
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
  }
}

// Initialize the background service
const focusGuard = new FocusGuardBackground();
