# FocusGuard Pro - Testing Guide

## Pre-Installation Setup

1. **Generate Icons**:
   - Open `create-icons.html` in your browser
   - Download the three generated icons (icon16.png, icon48.png, icon128.png)
   - Place them in the `icons/` folder

## Installation Steps

1. **Load Extension in Chrome**:
   - Open Chrome browser
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the FocusGuard Pro folder
   - Extension should appear in your extensions list

2. **Verify Installation**:
   - Look for the FocusGuard Pro icon in the Chrome toolbar
   - If not visible, click the puzzle piece icon and pin FocusGuard Pro

## Basic Functionality Tests

### Test 1: Popup Interface
1. Click the FocusGuard Pro icon
2. Verify popup opens with:
   - Header showing "FocusGuard Pro" with shield icon
   - Status indicator showing "Active"
   - Main toggle switch (should be active/blue)
   - Input field for adding websites
   - Current site section (if on a valid website)
   - Empty blocked sites list initially
   - Settings and Focus Mode buttons

### Test 2: Add a Blocked Site
1. In the popup, enter "example.com" in the input field
2. Click "Block" button
3. Verify:
   - Site appears in the blocked sites list
   - Site count updates
   - Success notification (check console if not visible)

### Test 3: Test Site Blocking
1. Navigate to `http://example.com` in a new tab
2. Verify:
   - Page redirects to the blocked page
   - Blocked page shows FocusGuard Pro branding
   - Shows the blocked URL
   - Displays productivity tips
   - Has "Go Back" and "Unblock Site" buttons

### Test 4: Unblock Site
1. On the blocked page, click "Unblock Site"
2. Select "Remove permanently"
3. Verify:
   - Site is removed from blocked list
   - Can now access the site normally

### Test 5: Settings Page
1. Right-click the extension icon
2. Select "Options"
3. Verify:
   - Settings page opens in new tab
   - Navigation sidebar works
   - Overview shows statistics
   - Block List section allows adding/removing sites
   - General settings toggles work

### Test 6: Current Site Blocking
1. Navigate to any website (e.g., google.com)
2. Open the popup
3. Verify current site section shows the website
4. Click "Block" button for current site
5. Verify site gets blocked immediately

## Advanced Tests

### Test 7: Extension Toggle
1. Open popup
2. Click the main toggle switch to disable
3. Verify:
   - Status changes to "Inactive"
   - Toggle switch becomes gray
   - Previously blocked sites are accessible

### Test 8: Settings Persistence
1. Open settings page
2. Toggle various settings (notifications, incognito mode, etc.)
3. Close and reopen settings
4. Verify settings are saved

### Test 9: Data Export/Import
1. In settings, add several blocked sites
2. Click "Export Data"
3. Verify JSON file downloads
4. Click "Clear All Data" and confirm
5. Click "Import Data" and select the exported file
6. Verify all data is restored

## Troubleshooting

### Common Issues:

1. **Extension doesn't load**:
   - Check that all files are present
   - Verify manifest.json syntax
   - Check browser console for errors

2. **Popup doesn't open**:
   - Verify popup files exist
   - Check for JavaScript errors in popup console

3. **Sites not blocking**:
   - Check background script console for errors
   - Verify extension has necessary permissions
   - Test with simple domains first

4. **Icons not showing**:
   - Generate icons using create-icons.html
   - Place in icons/ folder with correct names

### Debug Console Access:
- **Background Script**: Go to chrome://extensions/, find FocusGuard Pro, click "service worker"
- **Popup**: Right-click popup → Inspect
- **Options Page**: F12 on options page
- **Content Script**: F12 on any webpage

## Expected Behavior Summary

✅ **Working Features**:
- Basic website blocking via redirection
- Add/remove sites from block list
- Toggle extension on/off
- Popup interface with site management
- Settings page with configuration options
- Blocked page with user-friendly interface
- Data export/import functionality
- Current site blocking from popup

❌ **Not Yet Implemented** (Future Phases):
- Password protection
- Time-based unblocking
- Focus mode/Pomodoro timer
- Adult content filtering
- Keyword blocking
- Advanced analytics
- Cloud sync

## Performance Notes
- Extension should have minimal impact on browsing speed
- Storage operations should be fast
- UI should be responsive
- No memory leaks or excessive resource usage

## Security Considerations
- All data stored locally in Chrome storage
- No external network requests
- No sensitive data collection
- User has full control over blocked sites list

---

If all tests pass, Phase 1 implementation is successful! 🎉
