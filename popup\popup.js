// FocusGuard Pro - Popup JavaScript
// Phase 1: Basic popup functionality

class FocusGuardPopup {
  constructor() {
    this.blockedSites = [];
    this.isEnabled = true;
    this.currentTab = null;
    this.init();
  }

  async init() {
    // Show loading
    this.showLoading(true);

    try {
      // Get current tab
      await this.getCurrentTab();

      // Load data
      await this.loadData();

      // Setup event listeners
      this.setupEventListeners();

      // Update UI
      this.updateUI();

      // Hide loading
      this.showLoading(false);
    } catch (error) {
      console.error('Popup initialization error:', error);
      this.showLoading(false);
    }
  }

  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
    } catch (error) {
      console.error('Error getting current tab:', error);
    }
  }

  async loadData() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getBlockedSites' });
      if (response) {
        this.blockedSites = response.sites || [];
        this.isEnabled = response.isEnabled !== false;
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupEventListeners() {
    // Main toggle
    const mainToggle = document.getElementById('mainToggle');
    mainToggle.addEventListener('click', () => this.toggleBlocking());

    // Add site button
    const addSiteBtn = document.getElementById('addSiteBtn');
    addSiteBtn.addEventListener('click', () => this.addSite());

    // Site input enter key
    const siteInput = document.getElementById('siteInput');
    siteInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.addSite();
      }
    });

    // Toggle current site
    const toggleCurrentSiteBtn = document.getElementById('toggleCurrentSiteBtn');
    toggleCurrentSiteBtn.addEventListener('click', () => this.toggleCurrentSite());

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    settingsBtn.addEventListener('click', () => this.openSettings());

    // Focus mode button
    const focusModeBtn = document.getElementById('focusModeBtn');
    focusModeBtn.addEventListener('click', () => this.startFocusMode());
  }

  updateUI() {
    this.updateStatus();
    this.updateCurrentSite();
    this.updateBlockedSitesList();
  }

  updateStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const mainToggle = document.getElementById('mainToggle');

    if (this.isEnabled) {
      statusDot.classList.add('active');
      statusDot.classList.remove('inactive');
      statusText.textContent = 'Active';
      mainToggle.classList.add('active');
    } else {
      statusDot.classList.remove('active');
      statusDot.classList.add('inactive');
      statusText.textContent = 'Inactive';
      mainToggle.classList.remove('active');
    }
  }

  updateCurrentSite() {
    if (!this.currentTab || !this.currentTab.url) {
      document.getElementById('currentSiteSection').style.display = 'none';
      return;
    }

    const url = this.currentTab.url;
    const domain = this.extractDomain(url);

    if (!domain || url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
      document.getElementById('currentSiteSection').style.display = 'none';
      return;
    }

    document.getElementById('currentSiteSection').style.display = 'block';

    const siteIcon = document.getElementById('currentSiteIcon');
    const siteName = document.getElementById('currentSiteName');
    const siteStatus = document.getElementById('currentSiteStatus');
    const toggleBtn = document.getElementById('toggleCurrentSiteBtn');
    const toggleIcon = document.getElementById('toggleCurrentSiteIcon');
    const toggleText = document.getElementById('toggleCurrentSiteText');

    siteName.textContent = domain;

    const isBlocked = this.isSiteBlocked(domain);

    if (isBlocked) {
      siteStatus.textContent = 'Currently blocked';
      siteStatus.style.color = '#c53030';
      toggleIcon.textContent = '✓';
      toggleText.textContent = 'Unblock';
      toggleBtn.className = 'btn btn-accent';
    } else {
      siteStatus.textContent = 'Not blocked';
      siteStatus.style.color = '#718096';
      toggleIcon.textContent = '🚫';
      toggleText.textContent = 'Block';
      toggleBtn.className = 'btn btn-secondary';
    }

    // Set site icon
    siteIcon.textContent = this.getSiteIcon(domain);
    siteIcon.style.background = this.getSiteColor(domain);
  }

  updateBlockedSitesList() {
    const sitesList = document.getElementById('sitesList');
    const siteCount = document.getElementById('siteCount');
    const emptyState = document.getElementById('emptyState');

    siteCount.textContent = `${this.blockedSites.length} site${this.blockedSites.length !== 1 ? 's' : ''}`;

    if (this.blockedSites.length === 0) {
      sitesList.innerHTML = '';
      sitesList.appendChild(emptyState);
      return;
    }

    sitesList.innerHTML = '';

    this.blockedSites.forEach(domain => {
      const siteItem = this.createSiteItem(domain);
      sitesList.appendChild(siteItem);
    });
  }

  createSiteItem(domain) {
    const item = document.createElement('div');
    item.className = 'site-item';

    item.innerHTML = `
      <div class="site-info">
        <div class="site-icon" style="background: ${this.getSiteColor(domain)};">
          ${this.getSiteIcon(domain)}
        </div>
        <div class="site-details">
          <h4>${domain}</h4>
          <p>Blocked site</p>
        </div>
      </div>
      <button class="remove-btn" data-domain="${domain}">
        Remove
      </button>
    `;

    // Add remove functionality
    const removeBtn = item.querySelector('.remove-btn');
    removeBtn.addEventListener('click', () => this.removeSite(domain));

    return item;
  }

  async toggleBlocking() {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({ action: 'toggleBlocking' });
      if (response && response.success) {
        this.isEnabled = response.isEnabled;
        this.updateStatus();
      }
    } catch (error) {
      console.error('Error toggling blocking:', error);
    }

    this.showLoading(false);
  }

  async addSite() {
    const siteInput = document.getElementById('siteInput');
    const url = siteInput.value.trim();

    if (!url) return;

    const domain = this.extractDomain(url) || url;

    if (this.isSiteBlocked(domain)) {
      this.showNotification('Site is already blocked', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites.push(domain);
        siteInput.value = '';
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }

  async removeSite(domain) {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'removeBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites = this.blockedSites.filter(site => site !== domain);
        this.updateUI();
        this.showNotification(`${domain} has been unblocked`, 'success');
      }
    } catch (error) {
      console.error('Error removing site:', error);
      this.showNotification('Failed to unblock site', 'error');
    }

    this.showLoading(false);
  }

  async toggleCurrentSite() {
    if (!this.currentTab || !this.currentTab.url) return;

    const domain = this.extractDomain(this.currentTab.url);
    if (!domain) return;

    const isBlocked = this.isSiteBlocked(domain);

    if (isBlocked) {
      await this.removeSite(domain);
    } else {
      await this.addSiteByDomain(domain);
    }
  }

  async addSiteByDomain(domain) {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites.push(domain);
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }

  openSettings() {
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
    window.close();
  }

  startFocusMode() {
    // Open focus mode in a new popup window
    chrome.windows.create({
      url: chrome.runtime.getURL('popup/focus-mode.html'),
      type: 'popup',
      width: 450,
      height: 650,
      focused: true
    });

    // Close current popup
    window.close();
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return null;
    }
  }

  isSiteBlocked(domain) {
    return this.blockedSites.includes(domain);
  }

  getSiteIcon(domain) {
    const icons = {
      'facebook.com': 'f',
      'twitter.com': '𝕏',
      'youtube.com': '▶',
      'instagram.com': '📷',
      'reddit.com': '🤖',
      'tiktok.com': '🎵'
    };
    return icons[domain] || '🌐';
  }

  getSiteColor(domain) {
    const colors = {
      'facebook.com': '#1877F2',
      'twitter.com': '#1DA1F2',
      'youtube.com': '#FF0000',
      'instagram.com': '#E4405F',
      'reddit.com': '#FF4500',
      'tiktok.com': '#000000'
    };
    return colors[domain] || '#667eea';
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
      overlay.classList.add('show');
    } else {
      overlay.classList.remove('show');
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification - could be enhanced with a proper notification system
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FocusGuardPopup();
});
